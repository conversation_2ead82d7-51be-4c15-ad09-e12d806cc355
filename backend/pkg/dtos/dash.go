package dtos

import "github.com/google/uuid"

type ResponseForDashboard struct {
	TotalEvaluationCount int          `json:"total_evaluation_count" example:"12"`
	TotalSimulateCount   int          `json:"total_simulate_count" example:"8"`
	TotalSuccessCount    int          `json:"total_success_count" example:"8"`
	TotalErrorCount      int          `json:"total_error_count" example:"5"`
	LastSimulate         LastSimulate `json:"last_simulate"`
}

type LastSimulate struct {
	ID        uuid.UUID `json:"ID"  example:"123e4567-e89b-12d3-a456-************"`
	Name      string    `json:"name" example:"Simulation Name"`
	CreatedAt string    `json:"created_at" example:"2024-11-26T15:04:05Z"`
	Version   string    `json:"version" example:"v1.0.0"`
}

type FavoriteNode struct {
	Label string `json:"label" example:"Node Label"`
	Name  string `json:"name" example:"Node Name"`
	Type  string `json:"type" example:"Node Type"`
}

type ResponseForCurrentAdmin struct {
	Name             string `json:"name" example:"<PERSON> Doe"`
	Email            string `json:"email" example:"<EMAIL>"`
	OrganizationName string `json:"organization_name" example:"MonoPayments"`
}
