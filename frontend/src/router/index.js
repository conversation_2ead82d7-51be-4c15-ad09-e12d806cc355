import AuthRequire from "@/utils/AuthRequire";
import { createRouter, createWebHistory } from "vue-router";

import Dashboard from "./routes/dashboard";
import Admin from "./routes/admin"
import Organization from "./routes/organization"
import AccessToken from "./routes/access-token"
import Simulate from "./routes/simulate"
import ScheduledWorks from "./routes/scheduled-works";
import Profile from "./routes/profil";

let appRoutes = [].concat(
  Dashboard,
  Admin,
  Organization,
  AccessToken,
  Simulate,
  ScheduledWorks,
  Profile
);

const routes = [
  {
    path: "/",
    redirect: "/dashboard",
    component: () =>
      import("@/views/index.vue"),
    children: appRoutes,
    meta: {
      auth: true,
    },
  },
  {
    path: "/auth",
    redirect: "/auth/identifier",
    component: () =>
      import("@/views/auth/index.vue"),
    children: [{
      path: "/auth/identifier",
      name: "auth-identifier",
      component: () =>
        import("@/views/auth/identifier.vue"),
      meta: {
        auth: false,
        title: "Sign In",
      },
    },
    {
      path: "/auth/password",
      name: "auth-password",
      component: () =>
        import("@/views/auth/password.vue"),
      meta: {
        auth: false,
        title: "Sign In",
      },
    },
    // {
    //     path: "/auth/pin",
    //     name: "auth-pin",
    //     component: () =>
    //         import ("@/views/auth/pin.vue"),
    //     meta: {
    //         auth: false,
    //         title: "Pin Authentication",
    //     },
    // },
    // {
    //     path: "/auth/two-factor",
    //     name: "auth-two-factor",
    //     component: () =>
    //         import ("@/views/auth/two-factor.vue"),
    //     meta: {
    //         auth: false,
    //         title: "Two Factor Authentication",
    //     },
    // },
    {
      path: "/auth/forgot-password",
      name: "forgot-password",
      component: () =>
        import("@/views/auth/forgot-password.vue"),
      meta: {
        auth: false,
        title: "Forgot Password",
      },
    },
      // {
      //     path: "/auth/reset-password",
      //     name: "reset-password",
      //     component: () =>
      //         import ("@/views/auth/reset-password.vue"),
      //     meta: {
      //         auth: false,
      //         title: "Reset Password",
      //     },
      // },
      // {
      //     path: "/auth/challenge",
      //     name: "challenge",
      //     component: () =>
      //         import ("@/views/auth/challenge.vue"),
      //     meta: {
      //         auth: false,
      //         title: "Challenge",
      //     },
      // },
    ],
  },
  {
    path: "/:path(.*)",
    name: "404",
    component: () =>
      import("@/views/error/404.vue"),
    meta: {
      auth: false,
    },
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

const user = localStorage.user ? JSON.parse(localStorage.user) : null;
const title = user && user.organization && user.organization.name ? user.organization.name : "";
router.beforeEach((to, from, next) => {
  if (to.meta.title) {
    if (title !== "") {
      document.title = `${title.charAt(0).toUpperCase() + title.slice(1)} - ${to.meta.title.charAt(0).toUpperCase() + to.meta.title.slice(1)}`;
    } else {
      document.title = to.meta.title.charAt(0).toUpperCase() + to.meta.title.slice(1);
    }
  }
  if (to.name !== from.name) {
    window.scrollTo(0, 0);
  }

  AuthRequire(to, from, next);
});

export default router;