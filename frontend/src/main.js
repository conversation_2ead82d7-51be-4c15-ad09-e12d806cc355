import { createApp } from "vue";
import { i18n } from "@/plugins/i18n";

import App from "./App.vue";
import pinia from "./store/pinia";
import router from "./router";
import Toast from "@/plugins/toast";
import dayjs from "@/plugins/dayjs";

import { install as VueMonacoEditorPlugin } from "@guolao/vue-monaco-editor";
import { install as VueCodemirrorPlugin } from "vue-codemirror";

import "@/assets/css/tailwind.css";
import "@/assets/css/main.css";
import filters from "./filters";
const app = createApp(App);

app.use(pinia);
app.use(i18n);
app.use(dayjs);
app.use(router);
app.use(Toast);
app.use(VueMonacoEditorPlugin);
app.use(VueCodemirrorPlugin);

app.config.globalProperties.$filters = filters;
if (process.env.NODE_ENV === 'development') {
  app.config.warnHandler = () => null;
}

app.mount("body");
